# 创建一个空的数组来存储重命名记录
$renameRecords = @()

# 获取所有图片文件
$imageFiles = Get-ChildItem -Path . -Filter *.* | Where-Object { $_.Extension -match "\.(jpg|jpeg|png|gif|bmp|webp)$" }

foreach ($file in $imageFiles) {
    # 计算文件的MD5值
    $md5 = Get-FileHash -Path $file.FullName -Algorithm MD5
    
    # 新文件名 = MD5值 + 原始扩展名
    $newFileName = $md5.Hash.ToLower() + $file.Extension
    
    # 创建记录对象
    $record = [PSCustomObject]@{
        OriginalName = $file.Name
        MD5Name = $newFileName
    }
    
    # 添加到记录数组
    $renameRecords += $record
    
    # 重命名文件
    Rename-Item -Path $file.FullName -NewName $newFileName -Force
}

# 将记录导出到Markdown文件
$markdownContent = "# 图片重命名记录\n\n| 原文件名 | MD5文件名 |\n| -------- | -------- |\n"
foreach ($record in $renameRecords) {
    $markdownContent += "| $($record.OriginalName) | $($record.MD5Name) |\n"
}

# 保存Markdown文件
$markdownContent | Out-File -FilePath "rename_records.md" -Encoding utf8

Write-Host "所有图片已重命名为MD5值，记录已保存到 rename_records.md"
